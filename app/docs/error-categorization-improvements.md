# Error Categorization Improvements

## Overview

This document outlines the improvements made to the `categorizeLoginError` function and the overall error handling approach in the UI layer.

## Problems with the Original Implementation

### 1. Fragile String Parsing
The original `categorizeLoginError` function used string matching to categorize errors:

```go
// OLD - FRAGILE APPROACH
errStr := strings.ToLower(err.Error())
if strings.Contains(errStr, "connection refused") ||
   strings.Contains(errStr, "timeout") ||
   strings.Contains(errStr, "timed out") {
    return ErrorTypeNetwork
}
```

**Issues:**
- Error messages can vary between Go versions and operating systems
- Localization can change error messages
- Library updates can break string matching
- Load balancers/proxies return HTTP status codes, not raw network errors

### 2. Critical Logic in Wrong Layer
Authentication error categorization was embedded in UI handlers, making it:
- Hard to test in isolation
- Difficult to reuse across different handlers
- Mixed business logic with presentation logic

### 3. Ignored Structured Error Information
The API returns structured errors with `error_code` and HTTP status codes, but the old approach only looked at error message text.

## Industry Standard Solution

### 1. Proper Error Type Checking

Instead of string parsing, we now use Go's standard error handling patterns:

```go
// NEW - INDUSTRY STANDARD APPROACH
func isNetworkError(err error) bool {
    // Check for net.Error interface
    var netErr net.Error
    if errors.As(err, &netErr) {
        return true
    }

    // Check for specific network error types
    var opErr *net.OpError
    if errors.As(err, &opErr) {
        return true
    }

    // Check for syscall errors
    if errors.Is(err, syscall.ECONNREFUSED) {
        return true
    }

    return false
}
```

### 2. HTTP Status Code Priority

The new approach prioritizes HTTP status codes (most reliable) over error message parsing:

```go
func categorizeAuthError(statusCode int, errorCode, message string, cause error) AuthError {
    // Network errors first (from HTTP client failures)
    if cause != nil && isNetworkError(cause) {
        return AuthError{Type: ErrorTypeNetwork, ...}
    }

    // Then HTTP status codes (from API responses)
    switch statusCode {
    case http.StatusUnauthorized:
        return AuthError{Type: ErrorTypeAuthentication, ...}
    case http.StatusForbidden:
        return AuthError{Type: ErrorTypePermission, ...}
    // ...
    }
}
```

### 3. Structured Error Types

Introduced `AuthError` struct to carry structured error information:

```go
type AuthError struct {
    Type       UIErrorType
    StatusCode int
    ErrorCode  string    // From API response
    Message    string
    Cause      error     // Original error for debugging
}
```

## Benefits of the New Approach

### 1. Reliability
- **Error type checking** is immune to message changes
- **HTTP status codes** work consistently with load balancers and proxies
- **Syscall error checking** works across platforms

### 2. Maintainability
- Centralized error categorization logic
- Easy to test in isolation
- Clear separation of concerns

### 3. Debugging
- Preserves original error information
- Structured logging with error codes and status codes
- Better observability in production

### 4. Load Balancer/Proxy Compatibility
- Works correctly behind nginx, HAProxy, cloud load balancers
- Handles HTTP status codes from intermediary services
- No dependency on raw network error messages

## Real-World Scenarios

### Scenario 1: Behind Load Balancer
```
User -> Load Balancer -> signalsd API
```
- **Old approach**: Would see generic "authentication failed" for all errors
- **New approach**: Correctly categorizes based on HTTP status codes (401, 403, 500, etc.)

### Scenario 2: Network Issues
```
User -> signalsd UI -> (network failure) -> signalsd API
```
- **Old approach**: Fragile string matching that could break
- **New approach**: Proper `net.Error` interface checking

### Scenario 3: API Error Responses
```
API returns: {"error_code": "authentication_failure", "message": "Invalid credentials"}
```
- **Old approach**: Only looked at message text
- **New approach**: Uses both `error_code` and HTTP status code for categorization

## Testing

The new implementation includes comprehensive tests covering:
- Network error type detection
- HTTP status code categorization
- Error code mapping
- Edge cases and error wrapping

Run tests with:
```bash
go test ./internal/ui -v -run TestCategorizeAuthError
go test ./internal/ui -v -run TestIsNetworkError
```

## Migration Notes

- The `categorizeLoginError` method has been replaced with `categorizeAuthError` function
- Auth service now returns structured `AuthError` types
- Handlers use type assertions to handle structured errors
- Backward compatibility maintained for non-structured errors

This approach follows Go best practices and industry standards for error handling in distributed systems.

package ui

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v5"
	signalsd "github.com/information-sharing-networks/signalsd/app/internal/server/config"
)

type AuthService struct {
	apiBaseURL string
	httpClient *http.Client
}

type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// TokenStatus represents the status of an access token
type TokenStatus int

const (
	TokenMissing TokenStatus = iota
	TokenInvalid
	TokenExpired
	TokenValid
)

var tokenStatusNames = []string{"TokenMissing", "TokenInvalid", "TokenExpired", "TokenValid"}

func (t TokenStatus) String() string {
	if t < 0 || int(t) >= len(tokenStatusNames) {
		return fmt.Sprintf("TokenStatus(%d)", int(t))
	}
	return tokenStatusNames[t]
}

func NewAuthService(apiBaseURL string) *AuthService {
	return &AuthService{
		apiBaseURL: apiBaseURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// AuthenticateUser authenticates a user with the signalsd API and returns response with refresh token cookie
func (a *AuthService) AuthenticateUser(email, password string) (*LoginResponse, *http.Cookie, error) {
	loginReq := LoginRequest{
		Email:    email,
		Password: password,
	}

	jsonData, err := json.Marshal(loginReq)
	if err != nil {
		// Return structured system error for JSON marshaling failure
		uiErr := categorizeError(http.StatusInternalServerError, "system_error", "failed to marshal login request", err)
		return nil, nil, uiErr
	}

	url := fmt.Sprintf("%s/api/auth/login", a.apiBaseURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		// Return structured system error for request creation failure
		uiErr := categorizeError(http.StatusInternalServerError, "system_error", "failed to create request", err)
		return nil, nil, uiErr
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := a.httpClient.Do(req)
	if err != nil {
		// Log at CLIENT level - API dependency failure
		slog.Warn("API request failed",
			slog.String("component", "ui-client"),
			slog.String("operation", "authenticate_user"),
			slog.String("api_url", url),
			slog.String("error_type", "network"),
			slog.String("error", err.Error()),
		)
		// Return structured network error
		uiErr := categorizeError(0, "network_error", "failed to make request", err)
		return nil, nil, uiErr
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		// Log at CLIENT level - response reading failure
		slog.Warn("API response read failed",
			slog.String("component", "ui-client"),
			slog.String("operation", "authenticate_user"),
			slog.String("api_url", url),
			slog.Int("status_code", resp.StatusCode),
			slog.String("error", err.Error()),
		)
		// Return structured system error for response reading failure
		uiErr := categorizeError(http.StatusInternalServerError, "system_error", "failed to read response body", err)
		return nil, nil, uiErr
	}

	if resp.StatusCode != http.StatusOK {
		// Log at CLIENT level - API returned error
		slog.Warn("API returned error response",
			slog.String("component", "ui-client"),
			slog.String("operation", "authenticate_user"),
			slog.String("api_url", url),
			slog.Int("api_status_code", resp.StatusCode),
			slog.String("response_body", string(bodyBytes)),
		)

		var errorResp ErrorResponse
		if err := json.Unmarshal(bodyBytes, &errorResp); err != nil {
			// Return structured error even when we can't parse the response
			uiErr := categorizeError(resp.StatusCode, "", fmt.Sprintf("authentication failed with status %d", resp.StatusCode), nil)
			return nil, nil, uiErr
		}

		// Return structured error with API error details
		uiErr := categorizeError(resp.StatusCode, errorResp.ErrorCode, errorResp.Message, nil)
		return nil, nil, uiErr
	}

	// Log at CLIENT level - successful API call
	slog.Info("API request successful",
		slog.String("component", "ui-client"),
		slog.String("operation", "authenticate_user"),
		slog.String("api_url", url),
		slog.Int("api_status_code", resp.StatusCode),
	)

	var loginResp LoginResponse
	if err := json.Unmarshal(bodyBytes, &loginResp); err != nil {
		// Log at CLIENT level - response parsing failure
		slog.Warn("API response parse failed",
			slog.String("component", "ui-client"),
			slog.String("operation", "authenticate_user"),
			slog.String("api_url", url),
			slog.String("error", err.Error()),
		)
		// Return structured system error for JSON parsing failure
		uiErr := categorizeError(http.StatusInternalServerError, "system_error", "failed to decode response", err)
		return nil, nil, uiErr
	}

	// Extract the refresh token cookie from the API response
	cookies := resp.Cookies()
	var refreshTokenCookie *http.Cookie
	for _, cookie := range cookies {
		if cookie.Name == signalsd.RefreshTokenCookieName {
			refreshTokenCookie = cookie
			break
		}
	}

	if refreshTokenCookie == nil {
		return nil, nil, fmt.Errorf("refresh token cookie not found in API response")
	}

	return &loginResp, refreshTokenCookie, nil
}

// RefreshToken attempts to refresh an access token using the refresh token and returns new refresh token cookie
func (a *AuthService) RefreshToken(refreshTokenCookie *http.Cookie) (*LoginResponse, *http.Cookie, error) {
	url := fmt.Sprintf("%s/oauth/token?grant_type=refresh_token", a.apiBaseURL)

	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		uiErr := categorizeError(http.StatusInternalServerError, "system_error", "failed to create request", err)
		return nil, nil, uiErr
	}

	req.Header.Set("Content-Type", "application/json")

	// add the refresh token cookie from the browser's request to the API request
	req.AddCookie(refreshTokenCookie)

	resp, err := a.httpClient.Do(req)
	if err != nil {
		uiErr := categorizeError(0, "network_error", "failed to make request", err)
		return nil, nil, uiErr
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp ErrorResponse
		if err := json.NewDecoder(resp.Body).Decode(&errorResp); err != nil {
			uiErr := categorizeError(resp.StatusCode, "", fmt.Sprintf("token refresh failed with status %d", resp.StatusCode), nil)
			return nil, nil, uiErr
		}
		uiErr := categorizeError(resp.StatusCode, errorResp.ErrorCode, errorResp.Message, nil)
		return nil, nil, uiErr
	}

	var loginResp LoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&loginResp); err != nil {
		uiErr := categorizeError(http.StatusInternalServerError, "system_error", "failed to decode response", err)
		return nil, nil, uiErr
	}

	// Extract the new refresh token cookie from the API response
	cookies := resp.Cookies()
	var newRefreshTokenCookie *http.Cookie
	for _, cookie := range cookies {
		if cookie.Name == signalsd.RefreshTokenCookieName {
			newRefreshTokenCookie = cookie
			break
		}
	}

	if newRefreshTokenCookie == nil {
		return nil, nil, fmt.Errorf("new refresh token cookie not found in API response")
	}

	return &loginResp, newRefreshTokenCookie, nil
}

// CheckTokenStatus checks the status of an access token from the request cookies
func (a *AuthService) CheckTokenStatus(r *http.Request) TokenStatus {
	accessTokenCookie, err := r.Cookie(accessTokenCookieName)
	if err != nil {
		return TokenMissing
	}

	// Parse token without validation to check expiry
	parser := jwt.NewParser(jwt.WithoutClaimsValidation())
	claims := &jwt.RegisteredClaims{}

	_, _, err = parser.ParseUnverified(accessTokenCookie.Value, claims)
	if err != nil {
		return TokenInvalid
	}

	if claims.ExpiresAt == nil {
		return TokenInvalid
	}

	// Check if token is expired
	if claims.ExpiresAt.After(time.Now()) {
		return TokenValid
	}

	return TokenExpired
}

// SetAuthCookies sets the authentication-related cookies.
//
// The browser needs to maintain authentication state via cookies so that any signalsd instance can authenticate the user, regardless of which instance handles each request.
//
// The following cookies are set:
//   - refresh token cookie (forwarded from signalsd API)
//   - a cookie containing the access token provided by the server,
//   - a cookie containg the isn permissions as JSON.
//   - a cookie containing the account information (ID, type, role) as JSON.
func (a *AuthService) SetAuthCookies(w http.ResponseWriter, loginResp *LoginResponse, refreshTokenCookie *http.Cookie, environment string) error {
	isProd := environment == "prod"

	// Set refresh token cookie (from API response)
	http.SetCookie(w, refreshTokenCookie)

	// Set access token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     accessTokenCookieName,
		Value:    loginResp.AccessToken,
		Path:     "/",
		HttpOnly: true,
		Secure:   isProd,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   loginResp.ExpiresIn + 60, // JWT expiry + 1 minute buffer
	})

	// Set permissions cookie if permissions exist
	if len(loginResp.Perms) > 0 {
		permsJSON, err := json.Marshal(loginResp.Perms)
		if err != nil {
			return fmt.Errorf("failed to marshal permissions: %w", err)
		}

		// Base64 encode to avoid cookie encoding issues
		encodedPerms := base64.StdEncoding.EncodeToString(permsJSON)
		http.SetCookie(w, &http.Cookie{
			Name:     isnPermsCookieName,
			Value:    encodedPerms,
			Path:     "/",
			HttpOnly: true,
			Secure:   isProd,
			SameSite: http.SameSiteLaxMode,
			MaxAge:   loginResp.ExpiresIn + 60,
		})
	}

	// Set account information cookie (base64 encoded JSON)
	accountInfo := AccountInfo{
		AccountID:   loginResp.AccountID,
		AccountType: loginResp.AccountType,
		Role:        loginResp.Role,
	}

	accountInfoJSON, err := json.Marshal(accountInfo)
	if err != nil {
		return fmt.Errorf("failed to marshal account information: %w", err)
	}

	accountInfoBase64 := base64.StdEncoding.EncodeToString(accountInfoJSON)
	http.SetCookie(w, &http.Cookie{
		Name:     accountInfoCookieName,
		Value:    accountInfoBase64,
		Path:     "/",
		HttpOnly: true,
		Secure:   isProd,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   loginResp.ExpiresIn + 60, // JWT expiry + 1 minute buffer
	})

	return nil
}

// ClearAuthCookies clears all authentication-related cookies
func (a *AuthService) ClearAuthCookies(w http.ResponseWriter, environment string) {
	isProd := environment == "prod"

	http.SetCookie(w, &http.Cookie{
		Name:     accessTokenCookieName,
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   isProd,
		SameSite: http.SameSiteLaxMode,
	})

	http.SetCookie(w, &http.Cookie{
		Name:     signalsd.RefreshTokenCookieName,
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   isProd,
		SameSite: http.SameSiteLaxMode,
	})

	http.SetCookie(w, &http.Cookie{
		Name:     isnPermsCookieName,
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   isProd,
		SameSite: http.SameSiteLaxMode,
	})

	http.SetCookie(w, &http.Cookie{
		Name:     accountInfoCookieName,
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   isProd,
		SameSite: http.SameSiteLaxMode,
	})
}

// getIsnPermsFromCookie reads and decodes the ISN permissions from the cookie
func (s *Server) getIsnPermsFromCookie(r *http.Request) (map[string]IsnPerm, error) {
	permsCookie, err := r.Cookie(isnPermsCookieName)
	if err != nil {
		return make(map[string]IsnPerm), err
	}

	// Decode base64
	decodedPerms, err := base64.StdEncoding.DecodeString(permsCookie.Value)
	if err != nil {
		return make(map[string]IsnPerm), err
	}

	// Unmarshal JSON
	var isnPerms map[string]IsnPerm
	if err := json.Unmarshal(decodedPerms, &isnPerms); err != nil {
		return make(map[string]IsnPerm), err
	}

	return isnPerms, nil
}

func (s *Server) getAccountInfoFromCookie(r *http.Request) (*AccountInfo, error) {
	accountInfoCookie, err := r.Cookie(accountInfoCookieName)
	if err != nil {
		return nil, err
	}

	// Decode base64
	decodedAccountInfo, err := base64.StdEncoding.DecodeString(accountInfoCookie.Value)
	if err != nil {
		return nil, err
	}
	//unmarshal json to struct
	accountInfo := &AccountInfo{}
	if err := json.Unmarshal(decodedAccountInfo, accountInfo); err != nil {
		return nil, err
	}

	return accountInfo, nil
}

// getIsnPermission validates user has access to the ISN and returns the ISN permission deails (read/write, availalbe signal types, visibility)
func (s *Server) getIsnPermission(r *http.Request, isnSlug string) (*IsnPerm, error) {
	// Get permissions from cookie
	perms, err := s.getIsnPermsFromCookie(r)
	if err != nil {
		return nil, err
	}

	// Validate user has access to this ISN
	isnPerm, exists := perms[isnSlug]
	if !exists {
		return nil, fmt.Errorf("no permission for this ISN")
	}

	return &isnPerm, nil
}

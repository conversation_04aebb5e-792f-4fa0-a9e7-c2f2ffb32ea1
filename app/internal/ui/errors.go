package ui

import (
	"context"
	"log/slog"
	"net/http"
	"strings"

	"github.com/information-sharing-networks/signalsd/app/internal/logger"
)

type UIErrorType string

const (
	ErrorTypeValidation     UIErrorType = "validation"
	ErrorTypeAuthentication UIErrorType = "authentication"
	ErrorTypeNetwork        UIErrorType = "network"
	ErrorTypeSystem         UIErrorType = "system"
	ErrorTypePermission     UIErrorType = "permission"
)

// UIError represents a user-facing error
type UIError struct {
	Type        UIErrorType
	StatusCode  int
	UserMessage string
	LogMessage  string
}

// user-friendly error messages
var userErrorMessages = map[UIErrorType]string{
	ErrorTypeValidation:     "Please check your input and try again.",
	ErrorTypeAuthentication: "Login failed. Please check your email and password.",
	ErrorTypeNetwork:        "Unable to connect. Please try again in a few moments.",
	ErrorTypeSystem:         "A system error occurred. Please try again later.",
	ErrorTypePermission:     "You don't have permission to perform this action.",
}

// HTTP status codes for different error types
var errorStatusCodes = map[UIErrorType]int{
	ErrorTypeValidation:     http.StatusBadRequest,          // 400
	ErrorTypeAuthentication: http.StatusUnauthorized,        // 401
	ErrorTypeNetwork:        http.StatusServiceUnavailable,  // 503
	ErrorTypeSystem:         http.StatusInternalServerError, // 500
	ErrorTypePermission:     http.StatusForbidden,           // 403
}

// sanitizeAuthError converts technical authentication errors to user-friendly messages
// while preserving detailed logging for debugging
func (s *Server) sanitizeAuthError(err error, errorType UIErrorType) UIError {
	userMessage := userErrorMessages[errorType]
	statusCode := errorStatusCodes[errorType]

	// For authentication errors, use a generic message to prevent user enumeration
	if errorType == ErrorTypeAuthentication {
		userMessage = "Login failed. Please check your email and password."
	}

	return UIError{
		Type:        errorType,
		StatusCode:  statusCode,
		UserMessage: userMessage,
		LogMessage:  err.Error(),
	}
}

// renderUIError displays a sanitized error message to the user following HTMX best practices
func (s *Server) renderUIError(w http.ResponseWriter, r *http.Request, uiError UIError) {
	// Get request-scoped logger for immediate logging
	reqLogger := logger.ContextMiddlewareLogger(r.Context())

	// Log the technical error details
	reqLogger.Warn("UI error occurred",
		slog.String("component", "ui-error"),
		slog.String("error_type", string(uiError.Type)),
		slog.Int("status_code", uiError.StatusCode),
		slog.String("user_message", uiError.UserMessage),
		slog.String("technical_error", uiError.LogMessage),
	)

	// Add context for final request log
	logger.ContextWithLogAttrs(r.Context(),
		slog.String("error_type", string(uiError.Type)),
		slog.Int("error_status", uiError.StatusCode),
	)

	// Check if this is an HTMX request (form submission targeting a div)
	isHTMXRequest := r.Header.Get("HX-Request") == "true"

	if isHTMXRequest {
		// For HTMX requests, return HTTP 200 with error content for proper swapping
		// This follows HTMX best practices for form error handling
		w.WriteHeader(http.StatusOK)
	} else {
		// For direct requests, use appropriate HTTP status code
		w.WriteHeader(uiError.StatusCode)
	}

	// Render user-friendly error message
	component := AccessDeniedAlert(uiError.UserMessage)
	if err := component.Render(context.Background(), w); err != nil {
		s.logger.Error("Failed to render error component", slog.String("error", err.Error()))
	}
}

// renderUIErrorSilent renders error without logging (used when HTTP layer already logged)
func (s *Server) renderUIErrorSilent(w http.ResponseWriter, r *http.Request, uiError UIError) {
	// Check if this is an HTMX request (form submission targeting a div)
	isHTMXRequest := r.Header.Get("HX-Request") == "true"

	if isHTMXRequest {
		// For HTMX requests, return HTTP 200 with error content for proper swapping
		w.WriteHeader(http.StatusOK)
	} else {
		// For direct requests, use appropriate HTTP status code
		w.WriteHeader(uiError.StatusCode)
	}

	// Render user-friendly error message
	component := AccessDeniedAlert(uiError.UserMessage)
	if err := component.Render(context.Background(), w); err != nil {
		s.logger.Error("Failed to render error component", slog.String("error", err.Error()))
	}
}

// Helper functions for common error scenarios

// renderValidationError handles form validation errors
func (s *Server) renderValidationError(w http.ResponseWriter, r *http.Request, message string) {
	uiError := UIError{
		Type:        ErrorTypeValidation,
		StatusCode:  http.StatusBadRequest,
		UserMessage: message, // Validation messages are safe to show directly
		LogMessage:  message,
	}
	s.renderUIError(w, r, uiError)
}

// renderAuthenticationError handles authentication failures with generic messaging
func (s *Server) renderAuthenticationError(w http.ResponseWriter, r *http.Request, err error, errorType UIErrorType) {
	uiError := s.sanitizeAuthError(err, errorType)
	s.renderUIError(w, r, uiError)
}

// renderAuthenticationErrorSilent renders authentication errors without additional logging
// Used when the HTTP layer has already logged all necessary details
func (s *Server) renderAuthenticationErrorSilent(w http.ResponseWriter, r *http.Request, uiError UIError) {
	s.renderUIErrorSilent(w, r, uiError)
}

// renderSystemError handles system/technical errors
func (s *Server) renderSystemError(w http.ResponseWriter, r *http.Request, err error) {
	uiError := UIError{
		Type:        ErrorTypeSystem,
		StatusCode:  http.StatusInternalServerError,
		UserMessage: userErrorMessages[ErrorTypeSystem],
		LogMessage:  err.Error(),
	}
	s.renderUIError(w, r, uiError)
}

// renderPermissionError handles authorization/permission errors
func (s *Server) renderPermissionError(w http.ResponseWriter, r *http.Request, message string) {
	uiError := UIError{
		Type:        ErrorTypePermission,
		StatusCode:  http.StatusForbidden,
		UserMessage: message, // Permission messages are usually safe to show
		LogMessage:  message,
	}
	s.renderUIError(w, r, uiError)
}

// categorizeLoginError determines the error type based on the error content
// This helps provide appropriate user feedback while maintaining security
func (s *Server) categorizeLoginError(err error) UIErrorType {
	errStr := strings.ToLower(err.Error())

	// Network-related errors
	if strings.Contains(errStr, "network error") ||
		strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "timed out") ||
		strings.Contains(errStr, "failed to make request") ||
		strings.Contains(errStr, "dial tcp") {
		return ErrorTypeNetwork
	}

	// System/technical errors
	if strings.Contains(errStr, "failed to marshal") ||
		strings.Contains(errStr, "failed to decode") ||
		strings.Contains(errStr, "failed to create request") ||
		strings.Contains(errStr, "system error") {
		return ErrorTypeSystem
	}

	// Default to authentication error for security
	// This prevents information leakage about valid vs invalid accounts
	return ErrorTypeAuthentication
}

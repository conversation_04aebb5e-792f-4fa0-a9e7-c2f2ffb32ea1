package ui

import (
	"fmt"
	"net"
	"net/http"
	"net/url"
	"syscall"
	"testing"
)

// timeoutError implements net.Error for testing
type timeoutError struct{}

func (e *timeoutError) Error() string   { return "timeout" }
func (e *timeoutError) Timeout() bool   { return true }
func (e *timeoutError) Temporary() bool { return true }

func TestCategorizeAuthError(t *testing.T) {
	tests := []struct {
		name       string
		statusCode int
		errorCode  string
		message    string
		cause      error
		expected   UIErrorType
	}{
		// Network errors (using proper error types)
		{
			name:       "connection refused syscall error",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      syscall.ECONNREFUSED,
			expected:   ErrorTypeNetwork,
		},
		{
			name:       "network timeout error",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      &net.OpError{Op: "dial", Net: "tcp", Err: &net.DNSError{Name: "example.com", Err: "timeout"}},
			expected:   ErrorTypeNetwork,
		},
		{
			name:       "DNS lookup error",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      &net.DNSError{Name: "nonexistent.example.com", Err: "no such host"},
			expected:   ErrorTypeNetwork,
		},
		{
			name:       "URL error wrapping network error",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      &url.Error{Op: "Get", URL: "http://example.com", Err: syscall.ECONNREFUSED},
			expected:   ErrorTypeNetwork,
		},
		{
			name:       "timeout with net.Error interface",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      &timeoutError{},
			expected:   ErrorTypeNetwork,
		},

		// HTTP status code based categorization
		{
			name:       "401 unauthorized",
			statusCode: http.StatusUnauthorized,
			errorCode:  "authentication_failure",
			message:    "Invalid credentials",
			cause:      nil,
			expected:   ErrorTypeAuthentication,
		},
		{
			name:       "403 forbidden",
			statusCode: http.StatusForbidden,
			errorCode:  "access_denied",
			message:    "Access denied",
			cause:      nil,
			expected:   ErrorTypePermission,
		},
		{
			name:       "400 with authentication error code",
			statusCode: http.StatusBadRequest,
			errorCode:  "authentication_failure",
			message:    "Incorrect email or password",
			cause:      nil,
			expected:   ErrorTypeAuthentication,
		},
		{
			name:       "400 with resource not found (user not found)",
			statusCode: http.StatusBadRequest,
			errorCode:  "resource_not_found",
			message:    "No user found with this email address",
			cause:      nil,
			expected:   ErrorTypeAuthentication,
		},
		{
			name:       "400 with validation error",
			statusCode: http.StatusBadRequest,
			errorCode:  "malformed_body",
			message:    "Invalid JSON body",
			cause:      nil,
			expected:   ErrorTypeValidation,
		},
		{
			name:       "500 internal server error",
			statusCode: http.StatusInternalServerError,
			errorCode:  "database_error",
			message:    "Database connection failed",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},
		{
			name:       "502 bad gateway",
			statusCode: http.StatusBadGateway,
			errorCode:  "upstream_error",
			message:    "Upstream server error",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},
		{
			name:       "503 service unavailable",
			statusCode: http.StatusServiceUnavailable,
			errorCode:  "service_unavailable",
			message:    "Service temporarily unavailable",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},

		// Unknown status codes default to system error
		{
			name:       "unknown status code",
			statusCode: 418, // I'm a teapot (but gets normalized to 500)
			errorCode:  "teapot_error",
			message:    "I'm a teapot",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := categorizeAuthError(tt.statusCode, tt.errorCode, tt.message, tt.cause)

			if result.Type != tt.expected {
				t.Errorf("categorizeAuthError() = %v, want %v", result.Type, tt.expected)
			}

			// Verify other fields are set correctly
			if result.ErrorCode != tt.errorCode {
				t.Errorf("categorizeAuthError().ErrorCode = %v, want %v", result.ErrorCode, tt.errorCode)
			}

			if result.Message != tt.message {
				t.Errorf("categorizeAuthError().Message = %v, want %v", result.Message, tt.message)
			}

			if result.Cause != tt.cause {
				t.Errorf("categorizeAuthError().Cause = %v, want %v", result.Cause, tt.cause)
			}

			// Verify status code normalization
			expectedStatusCode := tt.statusCode
			if tt.expected == ErrorTypeAuthentication && tt.statusCode == http.StatusBadRequest {
				expectedStatusCode = http.StatusUnauthorized // Gets normalized
			} else if tt.expected == ErrorTypeSystem && tt.statusCode == 418 {
				expectedStatusCode = http.StatusInternalServerError // Gets normalized
			} else if tt.expected == ErrorTypeNetwork && tt.statusCode == 0 {
				expectedStatusCode = http.StatusServiceUnavailable // Gets set by categorizeAuthError
			}

			if result.StatusCode != expectedStatusCode {
				t.Errorf("categorizeAuthError().StatusCode = %v, want %v", result.StatusCode, expectedStatusCode)
			}
		})
	}
}

func TestAuthErrorImplementsError(t *testing.T) {
	// Test that AuthError implements the error interface
	authErr := AuthError{
		Type:       ErrorTypeAuthentication,
		StatusCode: http.StatusUnauthorized,
		ErrorCode:  "auth_failure",
		Message:    "Authentication failed",
		Cause:      fmt.Errorf("underlying error"),
	}

	// Should return the underlying error message when Cause is set
	if authErr.Error() != "underlying error" {
		t.Errorf("AuthError.Error() = %v, want %v", authErr.Error(), "underlying error")
	}

	// Should return the Message when Cause is nil
	authErr.Cause = nil
	if authErr.Error() != "Authentication failed" {
		t.Errorf("AuthError.Error() = %v, want %v", authErr.Error(), "Authentication failed")
	}
}

func TestIsNetworkError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		// Network errors that should be detected
		{
			name:     "syscall ECONNREFUSED",
			err:      syscall.ECONNREFUSED,
			expected: true,
		},
		{
			name:     "net.OpError",
			err:      &net.OpError{Op: "dial", Net: "tcp", Err: syscall.ECONNREFUSED},
			expected: true,
		},
		{
			name:     "net.DNSError",
			err:      &net.DNSError{Name: "example.com", Err: "no such host"},
			expected: true,
		},
		{
			name:     "url.Error wrapping network error",
			err:      &url.Error{Op: "Get", URL: "http://example.com", Err: syscall.ECONNREFUSED},
			expected: true,
		},
		{
			name:     "timeout error implementing net.Error",
			err:      &timeoutError{},
			expected: true,
		},

		// Non-network errors that should NOT be detected
		{
			name:     "generic error",
			err:      fmt.Errorf("some generic error"),
			expected: false,
		},
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isNetworkError(tt.err)
			if result != tt.expected {
				t.Errorf("isNetworkError(%v) = %v, want %v", tt.err, result, tt.expected)
			}
		})
	}
}

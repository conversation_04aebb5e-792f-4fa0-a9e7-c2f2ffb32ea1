package ui

import (
	"fmt"
	"net/http"
	"testing"
)

func TestCategorizeAuthError(t *testing.T) {
	tests := []struct {
		name       string
		statusCode int
		errorCode  string
		message    string
		cause      error
		expected   UIErrorType
	}{
		// Network errors (from HTTP client)
		{
			name:       "connection refused",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      fmt.<PERSON><PERSON>rf("dial tcp: connection refused"),
			expected:   ErrorTypeNetwork,
		},
		{
			name:       "timeout error",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      fmt.<PERSON><PERSON><PERSON>("context deadline exceeded (Client.Timeout exceeded)"),
			expected:   ErrorTypeNetwork,
		},
		{
			name:       "no such host",
			statusCode: 0,
			errorCode:  "network_error", // categorizeAuthError sets this
			message:    "",
			cause:      fmt.<PERSON>rrorf("dial tcp: lookup api.example.com: no such host"),
			expected:   ErrorTypeNetwork,
		},

		// HTTP status code based categorization
		{
			name:       "401 unauthorized",
			statusCode: http.StatusUnauthorized,
			errorCode:  "authentication_failure",
			message:    "Invalid credentials",
			cause:      nil,
			expected:   ErrorTypeAuthentication,
		},
		{
			name:       "403 forbidden",
			statusCode: http.StatusForbidden,
			errorCode:  "access_denied",
			message:    "Access denied",
			cause:      nil,
			expected:   ErrorTypePermission,
		},
		{
			name:       "400 with authentication error code",
			statusCode: http.StatusBadRequest,
			errorCode:  "authentication_failure",
			message:    "Incorrect email or password",
			cause:      nil,
			expected:   ErrorTypeAuthentication,
		},
		{
			name:       "400 with resource not found (user not found)",
			statusCode: http.StatusBadRequest,
			errorCode:  "resource_not_found",
			message:    "No user found with this email address",
			cause:      nil,
			expected:   ErrorTypeAuthentication,
		},
		{
			name:       "400 with validation error",
			statusCode: http.StatusBadRequest,
			errorCode:  "malformed_body",
			message:    "Invalid JSON body",
			cause:      nil,
			expected:   ErrorTypeValidation,
		},
		{
			name:       "500 internal server error",
			statusCode: http.StatusInternalServerError,
			errorCode:  "database_error",
			message:    "Database connection failed",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},
		{
			name:       "502 bad gateway",
			statusCode: http.StatusBadGateway,
			errorCode:  "upstream_error",
			message:    "Upstream server error",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},
		{
			name:       "503 service unavailable",
			statusCode: http.StatusServiceUnavailable,
			errorCode:  "service_unavailable",
			message:    "Service temporarily unavailable",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},

		// Unknown status codes default to system error
		{
			name:       "unknown status code",
			statusCode: 418, // I'm a teapot (but gets normalized to 500)
			errorCode:  "teapot_error",
			message:    "I'm a teapot",
			cause:      nil,
			expected:   ErrorTypeSystem,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := categorizeAuthError(tt.statusCode, tt.errorCode, tt.message, tt.cause)

			if result.Type != tt.expected {
				t.Errorf("categorizeAuthError() = %v, want %v", result.Type, tt.expected)
			}

			// Verify other fields are set correctly
			if result.ErrorCode != tt.errorCode {
				t.Errorf("categorizeAuthError().ErrorCode = %v, want %v", result.ErrorCode, tt.errorCode)
			}

			if result.Message != tt.message {
				t.Errorf("categorizeAuthError().Message = %v, want %v", result.Message, tt.message)
			}

			if result.Cause != tt.cause {
				t.Errorf("categorizeAuthError().Cause = %v, want %v", result.Cause, tt.cause)
			}

			// Verify status code normalization
			expectedStatusCode := tt.statusCode
			if tt.expected == ErrorTypeAuthentication && tt.statusCode == http.StatusBadRequest {
				expectedStatusCode = http.StatusUnauthorized // Gets normalized
			} else if tt.expected == ErrorTypeSystem && tt.statusCode == 418 {
				expectedStatusCode = http.StatusInternalServerError // Gets normalized
			} else if tt.expected == ErrorTypeNetwork && tt.statusCode == 0 {
				expectedStatusCode = http.StatusServiceUnavailable // Gets set by categorizeAuthError
			}

			if result.StatusCode != expectedStatusCode {
				t.Errorf("categorizeAuthError().StatusCode = %v, want %v", result.StatusCode, expectedStatusCode)
			}
		})
	}
}

func TestAuthErrorImplementsError(t *testing.T) {
	// Test that AuthError implements the error interface
	authErr := AuthError{
		Type:       ErrorTypeAuthentication,
		StatusCode: http.StatusUnauthorized,
		ErrorCode:  "auth_failure",
		Message:    "Authentication failed",
		Cause:      fmt.Errorf("underlying error"),
	}

	// Should return the underlying error message when Cause is set
	if authErr.Error() != "underlying error" {
		t.Errorf("AuthError.Error() = %v, want %v", authErr.Error(), "underlying error")
	}

	// Should return the Message when Cause is nil
	authErr.Cause = nil
	if authErr.Error() != "Authentication failed" {
		t.Errorf("AuthError.Error() = %v, want %v", authErr.Error(), "Authentication failed")
	}
}
